@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Bangladeshi SIM Offer Marketplace Design System */

@layer base {
  :root {
    /* Dark Theme Colors */
    --background: oklch(1.0000 0 0);
    --foreground: oklch(0.2101 0.0318 264.6645);

    --card: oklch(1.0000 0 0);
    --card-foreground: oklch(0.2101 0.0318 264.6645);

    --popover: oklch(1.0000 0 0);
    --popover-foreground: oklch(0.2101 0.0318 264.6645);

    /* Primary Green (WhatsApp) */
    --primary: oklch(0.5960 0.1274 163.2254);
    --primary-foreground: oklch(1.0000 0 0);

    /* Secondary Cyan */
    --secondary: oklch(0.9670 0.0029 264.5419);
    --secondary-foreground: oklch(0.2101 0.0318 264.6645);

    /* Muted colors */
    --muted: oklch(0.9670 0.0029 264.5419);
    --muted-foreground: oklch(0.5510 0.0234 264.3637);

    /* Accent colors */
    --accent: oklch(0.9670 0.0029 264.5419);
    --accent-foreground: oklch(0.2101 0.0318 264.6645);

    /* Status colors */
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1.0000 0 0);

    --success: 142 76% 36%;
    --warning: 43 96% 56%;
    --info: 217 91% 60%;

    /* Borders and inputs */
    --border: oklch(0.9276 0.0058 264.5313);
    --input: oklch(0.9276 0.0058 264.5313);
    --ring: oklch(0.5960 0.1274 163.2254);

    /* Custom marketplace colors */
    --operator-gp: 142 76% 36%;
    --operator-robi: 14 100% 57%;
    --operator-banglalink: 203 89% 53%;
    --operator-airtel: 0 84% 60%;
    --operator-skitto: 280 100% 70%;

    --text-light: 220 9% 83%;
    --text-muted: 220 9% 68%;
    --surface-elevated: 220 13% 20%;

    --radius: 0.5rem;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(var(--primary)), hsl(158 64% 45%));
    --gradient-card: linear-gradient(145deg, hsl(var(--card)), hsl(220 13% 16%));
    --gradient-success: linear-gradient(135deg, hsl(var(--success)), hsl(142 76% 30%));

    /* Shadows */
    --shadow-card: 0 4px 12px -2px hsl(220 13% 8% / 0.4);
    --shadow-elevated: 0 8px 25px -4px hsl(220 13% 8% / 0.6);
    --shadow-glow: 0 0 20px hsl(var(--primary) / 0.3);

    /* Animations */
    --transition-smooth: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: oklch(0.2101 0.0318 264.6645);

    --sidebar-primary: oklch(0.5960 0.1274 163.2254);

    --sidebar-primary-foreground: oklch(1.0000 0 0);

    --sidebar-accent: oklch(0.5960 0.1274 163.2254);

    --sidebar-accent-foreground: oklch(1.0000 0 0);

    --sidebar-border: oklch(0.9276 0.0058 264.5313);

    --sidebar-ring: oklch(0.5960 0.1274 163.2254);

    --chart-1: oklch(0.6959 0.1491 162.4796);

    --chart-2: oklch(0.6231 0.1880 259.8145);

    --chart-3: oklch(0.6368 0.2078 25.3313);

    --chart-4: oklch(0.7049 0.1867 47.6044);

    --chart-5: oklch(0.6056 0.2189 292.7172);

    --sidebar: oklch(0.9846 0.0017 247.8389);

    --font-sans: Geist;

    --font-serif: Lora;

    --font-mono: Geist Mono;

    --shadow-color: #000000;

    --shadow-opacity: 0.05;

    --shadow-blur: 4px;

    --shadow-spread: 0px;

    --shadow-offset-x: 0px;

    --shadow-offset-y: 2px;

    --letter-spacing: 0rem;

    --spacing: 0.25rem;

    --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.03);

    --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.03);

    --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);

    --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 1px 2px -1px hsl(0 0% 0% / 0.05);

    --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 2px 4px -1px hsl(0 0% 0% / 0.05);

    --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 4px 6px -1px hsl(0 0% 0% / 0.05);

    --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.05), 0px 8px 10px -1px hsl(0 0% 0% / 0.05);

    --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.13);

    --tracking-normal: 0rem;
  }
  .theme {
    --font-sans: Geist;
    --font-mono: Geist Mono;
    --font-serif: Lora;
    --radius: 0.5rem;
    --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
    --tracking-tight: calc(var(--tracking-normal) - 0.025em);
    --tracking-wide: calc(var(--tracking-normal) + 0.025em);
    --tracking-wider: calc(var(--tracking-normal) + 0.05em);
    --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  }
  .dark {
    --background: oklch(0.2101 0.0318 264.6645);
    --foreground: oklch(0.9846 0.0017 247.8389);
    --card: oklch(0.2781 0.0296 256.8480);
    --card-foreground: oklch(0.9846 0.0017 247.8389);
    --popover: oklch(0.2781 0.0296 256.8480);
    --popover-foreground: oklch(0.9846 0.0017 247.8389);
    --primary: oklch(0.6959 0.1491 162.4796);
    --primary-foreground: oklch(1.0000 0 0);
    --secondary: oklch(0.3729 0.0306 259.7328);
    --secondary-foreground: oklch(0.9846 0.0017 247.8389);
    --muted: oklch(0.3729 0.0306 259.7328);
    --muted-foreground: oklch(0.7137 0.0192 261.3246);
    --accent: oklch(0.6959 0.1491 162.4796);
    --accent-foreground: oklch(1.0000 0 0);
    --destructive: oklch(0.6368 0.2078 25.3313);
    --destructive-foreground: oklch(1.0000 0 0);
    --border: oklch(0.3729 0.0306 259.7328);
    --input: oklch(0.3729 0.0306 259.7328);
    --ring: oklch(0.6959 0.1491 162.4796);
    --chart-1: oklch(0.6959 0.1491 162.4796);
    --chart-2: oklch(0.6231 0.1880 259.8145);
    --chart-3: oklch(0.6368 0.2078 25.3313);
    --chart-4: oklch(0.7049 0.1867 47.6044);
    --chart-5: oklch(0.6056 0.2189 292.7172);
    --radius: 0.5rem;
    --sidebar: oklch(0.2101 0.0318 264.6645);
    --sidebar-foreground: oklch(0.9846 0.0017 247.8389);
    --sidebar-primary: oklch(0.6959 0.1491 162.4796);
    --sidebar-primary-foreground: oklch(1.0000 0 0);
    --sidebar-accent: oklch(0.6959 0.1491 162.4796);
    --sidebar-accent-foreground: oklch(1.0000 0 0);
    --sidebar-border: oklch(0.3729 0.0306 259.7328);
    --sidebar-ring: oklch(0.6959 0.1491 162.4796);
    --font-sans: Geist;
    --font-serif: Lora;
    --font-mono: Geist Mono;
    --shadow-color: #000000;
    --shadow-opacity: 0.1;
    --shadow-blur: 4px;
    --shadow-spread: 0px;
    --shadow-offset-x: 0px;
    --shadow-offset-y: 2px;
    --letter-spacing: 0rem;
    --spacing: 0.25rem;
    --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 1px 2px -1px hsl(0 0% 0% / 0.10);
    --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 2px 4px -1px hsl(0 0% 0% / 0.10);
    --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 4px 6px -1px hsl(0 0% 0% / 0.10);
    --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.10), 0px 8px 10px -1px hsl(0 0% 0% / 0.10);
    --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.25);
  }
  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer components {
  /* Custom Marketplace Components */
  .offer-card {
    @apply bg-card border border-border rounded-2xl p-4 shadow-md transition-all duration-200 hover:shadow-lg hover:scale-[1.03] hover:border-primary/40;
    /* Mobile-first, expressive elevation, soft corners, smooth scale */
  }

  /* MCP Responsive Guidance: 
     For responsiveness, use shadcn/ui components (Card, Button, Tabs, etc.) in your React code and apply Tailwind responsive classes (sm:, md:, lg:) directly in JSX. 
     Avoid duplicating responsive logic in global CSS. 
     Example:
     <Card className="w-full sm:max-w-md md:max-w-lg p-4 sm:p-6 md:p-8" />
     <Button className="w-full sm:w-auto h-10 sm:h-11 md:h-12" />
     <Tabs className="flex-col md:flex-row" />
  */

  .price-original {
    @apply text-muted-foreground text-base line-through;
    /* Slightly larger for mobile clarity */
  }

  .price-current {
    @apply text-primary text-xl font-bold;
    /* Bolder, larger for mobile */
  }

  .savings-badge {
    @apply bg-success/20 text-success px-3 py-1 rounded-full text-xs font-semibold shadow-sm;
    /* Rounded, expressive, mobile-first */
  }

  .region-badge {
    @apply bg-warning/20 text-warning px-3 py-1 rounded-full text-xs font-semibold shadow-sm;
  }

  .combo-badge {
    @apply bg-success/20 text-success px-3 py-1 rounded-full text-xs font-semibold shadow-sm;
  }

  .shimmer {
    @apply animate-pulse bg-gradient-to-r from-muted via-muted/50 to-muted rounded-xl;
  }

  .mobile-container {
    @apply w-full max-w-full mx-auto bg-background min-h-screen px-2 sm:px-0 flex flex-col gap-4;
    /* Mobile-first, vertical spacing, full width */
  }

  .stats-text {
    @apply text-muted-foreground text-base;
  }

  .balance-display {
    @apply text-primary font-bold text-base flex items-center gap-2;
  }

  /* Typography System */
  .heading-xl { @apply text-2xl font-bold leading-tight; }
  .heading-lg { @apply text-xl sm:text-2xl font-bold leading-tight tracking-tight; }
  .heading-md { @apply text-lg sm:text-xl font-semibold leading-tight tracking-tight; }
  .heading-sm { @apply text-base sm:text-lg font-semibold leading-tight tracking-tight; }

  .body-lg { @apply text-base leading-relaxed; }
  .body-md { @apply text-sm sm:text-base leading-relaxed tracking-normal; }
  .body-sm { @apply text-xs sm:text-sm leading-relaxed tracking-normal; }

  .text-muted { @apply text-muted-foreground; }
  .text-light { @apply text-[hsl(var(--text-light))]; }

  /* Admin Panel Styles */
  .admin-card {
    @apply bg-white border border-gray-200 rounded-lg p-6 shadow-sm;
  }

  .upload-zone {
    @apply border-2 border-dashed border-gray-300 rounded-lg p-8
           text-center hover:border-blue-400 transition-colors
           bg-gray-50 hover:bg-gray-100;
  }

  /* Scrollbar utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}